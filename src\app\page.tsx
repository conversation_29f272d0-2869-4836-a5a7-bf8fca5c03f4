'use client'

import { useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { useAuthStore } from '@/stores/authStore'

export default function Home() {
  const router = useRouter()
  const { isAuthenticated } = useAuthStore()

  useEffect(() => {
    if (isAuthenticated) {
      router.replace('/program')
      return
    }

    // Delay redirect to login to show animation
    const timer = setTimeout(() => {
      router.replace('/login')
    }, 800)

    return () => clearTimeout(timer)
  }, [isAuthenticated, router])

  return (
    <main className="flex min-h-[100dvh] flex-col items-center justify-center p-24">
      <div className="animate-pulse">
        <h1 className="text-4xl font-bold text-brand-primary">Dr. Muscle X</h1>
        <p className="mt-4 text-lg text-text-secondary">
          World's Fastest AI Personal Trainer
        </p>
      </div>
    </main>
  )
}
