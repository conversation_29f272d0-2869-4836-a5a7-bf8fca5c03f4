'use client'

import { useEffect, useState } from 'react'
import { useRouter } from 'next/navigation'
import { SetScreen } from '@/components/workout/SetScreen'
import { SetScreenLoadingState } from '@/components/workout/SetScreenLoadingState'
import { useWorkout } from '@/hooks/useWorkout'
import { useWorkoutStore } from '@/stores/workoutStore'

interface ExercisePageClientProps {
  exerciseId: number
}

export function ExercisePageClient({ exerciseId }: ExercisePageClientProps) {
  const router = useRouter()
  const [isInitializing, setIsInitializing] = useState(true)
  const [loadingError, setLoadingError] = useState<Error | null>(null)
  const {
    todaysWorkout,
    isLoadingWorkout,
    workoutError,
    startWorkout,
    exercises,
    workoutSession,
    loadRecommendation,
    updateExerciseWorkSets,
  } = useWorkout()
  const {
    setCurrentExerciseById,
    loadingStates,
    getCachedExerciseRecommendation,
  } = useWorkoutStore()

  // eslint-disable-next-line no-console
  console.log('🏋️ [ExercisePageClient] Component rendered', {
    exerciseId,
    hasWorkoutSession: !!workoutSession,
    hasTodaysWorkout: !!todaysWorkout,
    isLoadingWorkout,
    exercisesCount: exercises?.length || 0,
    isInitializing,
  })

  // Retry function for manual initialization
  const retryInitialization = async () => {
    try {
      setIsInitializing(true)
      setLoadingError(null)

      // Start workout if needed
      if (!workoutSession && todaysWorkout && !isLoadingWorkout) {
        const workoutGroup = todaysWorkout[0]
        const workout = workoutGroup?.WorkoutTemplates?.[0]

        if (workout) {
          await startWorkout(todaysWorkout)
        } else {
          router.replace('/workout')
          return
        }
      }

      // Set current exercise and load recommendations
      if (exerciseId) {
        setCurrentExerciseById(exerciseId)

        // Check if recommendation is loaded for this exercise
        const hasRecommendation = getCachedExerciseRecommendation(exerciseId)
        const isLoadingRecommendation = loadingStates.get(exerciseId)

        // If no recommendation and not loading, trigger loading
        if (!hasRecommendation && !isLoadingRecommendation) {
          const exercise = exercises?.find((ex) => ex.Id === exerciseId)
          if (exercise) {
            loadRecommendation(exerciseId, exercise.Label || 'Exercise')
          }
        }

        // Pre-load recommendation if not already loaded using alternative method
        const exercise = exercises?.find((ex) => ex.Id === exerciseId)
        if (exercise && (!exercise.sets || exercise.sets.length === 0)) {
          updateExerciseWorkSets(exerciseId, [])
        }
      }
    } catch (error) {
      console.error('Failed to retry initialization:', error)
      setLoadingError(
        error instanceof Error
          ? error
          : new Error('Failed to retry initialization')
      )
    } finally {
      setIsInitializing(false)
    }
  }

  // Split initialization into separate effects to avoid dependency issues

  // Effect 1: Start workout if needed
  useEffect(() => {
    async function startWorkoutIfNeeded() {
      try {
        setLoadingError(null)

        if (!workoutSession && todaysWorkout && !isLoadingWorkout) {
          // eslint-disable-next-line no-console
          console.log('🚀 [ExercisePageClient] Starting workout...', {
            todaysWorkout,
          })

          const workoutGroup = todaysWorkout[0]
          const workout = workoutGroup?.WorkoutTemplates?.[0]

          if (workout) {
            await startWorkout(todaysWorkout)
            // eslint-disable-next-line no-console
            console.log('✅ [ExercisePageClient] Workout started successfully')
          } else {
            // eslint-disable-next-line no-console
            console.error(
              '❌ [ExercisePageClient] No workout template found, redirecting...'
            )
            router.replace('/workout')
          }
        }
      } catch (error) {
        console.error('Failed to start workout:', error)
        setLoadingError(
          error instanceof Error ? error : new Error('Failed to start workout')
        )
      }
    }

    startWorkoutIfNeeded()
  }, [workoutSession, todaysWorkout, isLoadingWorkout, startWorkout, router])

  // Effect 2: Set current exercise and load recommendations
  useEffect(() => {
    async function setupExercise() {
      try {
        if (!exerciseId) return

        // eslint-disable-next-line no-console
        console.log('🎯 [ExercisePageClient] Setting up exercise', {
          exerciseId,
          hasExercises: !!exercises,
          exercisesCount: exercises?.length || 0,
        })

        // Set current exercise by ID
        setCurrentExerciseById(exerciseId)

        // Check if recommendation is loaded for this exercise
        const hasRecommendation = getCachedExerciseRecommendation(exerciseId)
        const isLoadingRecommendation = loadingStates.get(exerciseId)

        // eslint-disable-next-line no-console
        console.log('💭 [ExercisePageClient] Recommendation status', {
          exerciseId,
          hasRecommendation: !!hasRecommendation,
          isLoadingRecommendation,
        })

        // If no recommendation and not loading, trigger loading
        if (!hasRecommendation && !isLoadingRecommendation) {
          const exercise = exercises?.find((ex) => ex.Id === exerciseId)
          // eslint-disable-next-line no-console
          console.log('🔍 [ExercisePageClient] Found exercise', {
            exerciseId,
            exercise,
            exercises: exercises?.map((ex) => ({ id: ex.Id, label: ex.Label })),
          })

          if (exercise) {
            // eslint-disable-next-line no-console
            console.log('📡 [ExercisePageClient] Loading recommendation...', {
              exerciseId,
              exerciseLabel: exercise.Label,
            })
            loadRecommendation(exerciseId, exercise.Label || 'Exercise')
          } else {
            // eslint-disable-next-line no-console
            console.error(
              '❌ [ExercisePageClient] Exercise not found in exercises list',
              {
                exerciseId,
                availableIds: exercises?.map((ex) => ex.Id),
              }
            )
          }
        }

        // Pre-load recommendation if not already loaded using alternative method
        const exercise = exercises?.find((ex) => ex.Id === exerciseId)
        if (exercise && (!exercise.sets || exercise.sets.length === 0)) {
          // eslint-disable-next-line no-console
          console.log(
            '🔧 [ExercisePageClient] Updating exercise work sets to empty array'
          )
          updateExerciseWorkSets(exerciseId, [])
        }
      } catch (error) {
        console.error('Failed to setup exercise:', error)
        setLoadingError(
          error instanceof Error ? error : new Error('Failed to setup exercise')
        )
      } finally {
        setIsInitializing(false)
      }
    }

    setupExercise()
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [exerciseId]) // Only depend on exerciseId to prevent infinite loops

  // Show error state with retry option
  if (loadingError) {
    return (
      <div className="flex flex-col items-center justify-center min-h-[50vh] p-4">
        <p className="text-red-600 mb-4">
          Failed to load exercise: {loadingError.message}
        </p>
        <button
          className="px-4 py-2 bg-blue-600 text-white rounded-lg mb-2"
          onClick={retryInitialization}
        >
          Retry
        </button>
        <button
          className="px-4 py-2 bg-gray-600 text-white rounded-lg"
          onClick={() => router.push('/workout')}
        >
          Back to Workout
        </button>
      </div>
    )
  }

  // Handle workout errors
  if (workoutError) {
    return (
      <div className="flex items-center justify-center min-h-[100dvh] bg-gray-50">
        <div className="text-center p-6 max-w-md">
          <h2 className="text-2xl font-bold text-red-600 mb-4">
            Failed to Load Workout
          </h2>
          <p className="text-gray-600 mb-6">
            {typeof workoutError === 'string'
              ? workoutError
              : workoutError.message || 'Unable to load workout data.'}
          </p>
          <button
            onClick={() => router.push('/workout')}
            className="w-full px-6 py-3 bg-blue-600 text-white rounded-lg font-semibold hover:bg-blue-700 transition-colors min-h-[44px]"
          >
            Back to Workout
          </button>
        </div>
      </div>
    )
  }

  // Check if we're still loading recommendations for this exercise
  const isLoadingRecommendation = exerciseId
    ? loadingStates.get(exerciseId)
    : false

  // eslint-disable-next-line no-console
  console.log('🎨 [ExercisePageClient] Rendering decision', {
    isInitializing,
    isLoadingWorkout,
    isLoadingRecommendation,
    hasError: !!loadingError || !!workoutError,
  })

  // Show loading while initializing
  if (isInitializing || isLoadingWorkout || isLoadingRecommendation) {
    return <SetScreenLoadingState />
  }

  // Render the SetScreen once everything is ready
  return <SetScreen exerciseId={exerciseId} />
}
