import React from 'react'

interface FloatingCTAButtonProps {
  onClick: () => void
  label?: string
  ariaLabel?: string
}

export function FloatingCTAButton({
  onClick,
  label = 'Open Workout',
  ariaLabel = 'Open Workout - Start your next workout session',
}: FloatingCTAButtonProps) {
  return (
    <div
      data-testid="floating-cta-container"
      className="fixed bottom-6 left-0 right-0 z-50 px-4"
      role="navigation"
      aria-label="Primary actions"
    >
      <div className="max-w-lg mx-auto w-full">
        <button
          onClick={onClick}
          className="w-full px-6 py-4 min-h-[56px] bg-brand-primary text-text-inverse font-semibold rounded-full hover:bg-brand-primary/90 active:scale-[0.98] shadow-theme-xl hover:shadow-theme-2xl transition-all focus:outline-none focus:ring-2 focus:ring-brand-primary/50 focus:ring-offset-2 focus:ring-offset-bg-primary"
          aria-label={ariaLabel}
          data-testid="start-workout-button"
        >
          {label}
        </button>
      </div>
    </div>
  )
}
