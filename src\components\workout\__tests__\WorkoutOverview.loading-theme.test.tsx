import React from 'react'
import { render, screen } from '@testing-library/react'
import { WorkoutOverview } from '../WorkoutOverview'
import { useWorkout } from '@/hooks/useWorkout'
import { vi } from 'vitest'

// Mock dependencies
vi.mock('@/hooks/useWorkout')
vi.mock('next/navigation', () => ({
  useRouter: () => ({
    push: vi.fn(),
  }),
}))
vi.mock('@/hooks/usePullToRefresh', () => ({
  usePullToRefresh: () => ({
    isRefreshing: false,
    pullDistance: 0,
    isPulling: false,
  }),
}))

describe('WorkoutOverview - Loading State Theme', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  it('should use theme colors in loading state instead of hardcoded white', () => {
    // Mock loading state
    vi.mocked(useWorkout).mockReturnValue({
      todaysWorkout: null,
      isLoadingWorkout: true,
      workoutError: null,
      startWorkout: vi.fn(),
      userProgramInfo: null,
      exercises: [],
      exerciseWorkSetsModels: {},
      expectedExerciseCount: 0,
      hasInitialData: false,
      isLoadingFresh: false,
      isOffline: false,
      refreshWorkout: vi.fn(),
      updateExerciseWorkSets: vi.fn(),
      workoutSession: null,
      finishWorkout: vi.fn(),
      isLoading: true,
      loadExerciseRecommendation: vi.fn(),
    } as any)

    render(<WorkoutOverview />)

    // Check that the loading container uses theme colors
    const fixedContainer = screen
      .getByTestId('workout-overview')
      .querySelector('.fixed.bottom-0')
    expect(fixedContainer).toBeInTheDocument()

    // Should have theme background classes, not bg-white
    expect(fixedContainer).toHaveClass('bg-bg-secondary')
    expect(fixedContainer).not.toHaveClass('bg-white')

    // Should have theme border classes, not border-gray-200
    expect(fixedContainer).toHaveClass('border-bg-tertiary')
    expect(fixedContainer).not.toHaveClass('border-gray-200')

    // Check button has theme colors
    const loadingButton = screen.getByTestId('start-workout-button')
    expect(loadingButton).toHaveClass('bg-bg-tertiary')
    expect(loadingButton).toHaveClass('text-text-secondary')
    expect(loadingButton).not.toHaveClass('bg-gray-400')
    expect(loadingButton).not.toHaveClass('text-white')
  })

  it('should maintain theme consistency throughout loading states', () => {
    // Start with loading state
    const { rerender } = render(<WorkoutOverview />)

    // Mock initial loading
    vi.mocked(useWorkout).mockReturnValue({
      todaysWorkout: null,
      isLoadingWorkout: true,
      workoutError: null,
      startWorkout: vi.fn(),
      userProgramInfo: null,
      exercises: [],
      exerciseWorkSetsModels: {},
      expectedExerciseCount: 0,
      hasInitialData: false,
      isLoadingFresh: false,
      isOffline: false,
      refreshWorkout: vi.fn(),
      updateExerciseWorkSets: vi.fn(),
      workoutSession: null,
      finishWorkout: vi.fn(),
      isLoading: true,
      loadExerciseRecommendation: vi.fn(),
    } as any)

    rerender(<WorkoutOverview />)

    // Check no white backgrounds in any state
    const allElements = screen
      .getByTestId('workout-overview')
      .querySelectorAll('*')
    allElements.forEach((element) => {
      const classList = element.getAttribute('class') || ''
      expect(classList).not.toContain('bg-white')
      expect(classList).not.toContain('border-gray')
    })
  })
})
