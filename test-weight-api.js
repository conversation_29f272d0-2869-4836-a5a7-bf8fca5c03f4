/**
 * Simple Node.js script to test the weight recommendation API directly
 * This bypasses the browser and tests the API endpoints directly
 */

const fetch = require('node-fetch');
const FormData = require('form-data');

const a = '<EMAIL>';
const b = 'Dr123456';

const API_BASE_URL = 'https://drmuscle.azurewebsites.net';

async function login(username, password) {
  const params = new URLSearchParams();
  params.append('grant_type', 'password');
  params.append('username', username.toLowerCase());
  params.append('password', password);

  const response = await fetch(`${API_BASE_URL}/token`, {
    method: 'POST',
    body: params,
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
  });

  if (!response.ok) {
    console.error('Login failed. Status:', response.status, response.statusText);
    const text = await response.text();
    console.error('Response body:', text);
    throw new Error(`Lo<PERSON> failed with status: ${response.status}`);
  }

  const data = await response.json();
  return data.access_token;
}

async function makeRequest(endpoint, token, body) {
  const response = await fetch(`${API_BASE_URL}${endpoint}`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      Authorization: `Bearer ${token}`,
    },
    body: JSON.stringify(body),
  });

  if (!response.ok) {
    console.error(`API request to ${endpoint} failed with status: ${response.status}`);
    const errorBody = await response.text();
    console.error('Error body:', errorBody);
    return null;
  }

  return response.json();
}

async function getWorkouts(token) {
    const timeZone = Intl.DateTimeFormat().resolvedOptions().timeZone;
    const body = { timeZone };
    return makeRequest('/api/WorkoutLog/GetUserWorkoutProgramTimeZoneInfo', token, body);
}

async function getRecommendation(token, exercise, workoutId) {
    const { Id: exerciseId, SetStyle, IsFlexibility } = exercise;

    const endpoint =
    SetStyle === 'Normal' || IsFlexibility
      ? '/api/Exercise/GetRecommendationNormalRIRForExerciseWithoutWarmupsNew'
      : '/api/Exercise/GetRecommendationRestPauseRIRForExerciseWithoutWarmupsNew';

    const requestBody = {
        Username: a.toLowerCase(),
        ExerciseId: exerciseId,
        WorkoutId: workoutId,
        IsQuickMode: false,
        LightSessionDays: 0,
        IsStrengthPhashe: false, // Watch out for this typo in the API
        IsFreePlan: false,
        IsFirstWorkoutOfStrengthPhase: false,
        VersionNo: 1,
      };

    console.log(`Requesting recommendation for exercise ${exerciseId} with body:`, JSON.stringify(requestBody, null, 2));
    return makeRequest(endpoint, token, requestBody);
}


async function main() {
  try {
    const token = await login(a, b);
    console.log('Login successful.');

    const workoutData = await getWorkouts(token);
    if (!workoutData || !workoutData.Result) {
        console.log('Could not retrieve workout data or result is missing.');
        return;
    }

    const nextWorkout = workoutData.Result.GetUserProgramInfoResponseModel?.NextWorkoutTemplate;
    const exercises = nextWorkout?.Exercises;

    if (!nextWorkout || !exercises || exercises.length === 0) {
        console.log('No upcoming workouts or exercises found.');
        console.log('Workout Data:', JSON.stringify(workoutData, null, 2));
        return;
    }
    
    console.log(`Found workout: "${nextWorkout.Label}" with ${exercises.length} exercises.`);

    const firstExercise = exercises[0];
    console.log(`Attempting to get recommendation for first exercise: "${firstExercise.Label}" (ID: ${firstExercise.Id})`);

    const recommendation = await getRecommendation(token, firstExercise, nextWorkout.Id);

    console.log('\n--- Recommendation Result ---');
    console.log(JSON.stringify(recommendation, null, 2));
    console.log('---------------------------\n');

    if (recommendation && recommendation.Result && recommendation.Result.Series > 0) {
        console.log('✅ Successfully retrieved recommendation!');
    } else {
        console.log('❌ Failed to retrieve a valid recommendation. Result was null or had no sets.');
    }

  } catch (error) {
    console.error('An error occurred in main:', error.message);
  }
}

main();
