import { describe, it, expect, beforeEach, vi, afterEach } from 'vitest'
import { render, screen } from '@testing-library/react'
import Home from '@/app/page'
import { useRouter } from 'next/navigation'
import { useAuthStore } from '@/stores/authStore'

// Mock dependencies
vi.mock('next/navigation')
vi.mock('@/stores/authStore')

describe('Home Page', () => {
  const mockReplace = vi.fn()

  beforeEach(() => {
    vi.clearAllMocks()
    vi.useFakeTimers()

    vi.mocked(useRouter).mockReturnValue({
      replace: mockReplace,
      push: vi.fn(),
      back: vi.fn(),
      forward: vi.fn(),
      refresh: vi.fn(),
      prefetch: vi.fn(),
    } as ReturnType<typeof useRouter>)
  })

  afterEach(() => {
    vi.useRealTimers()
  })

  it('should redirect to /program when authenticated', () => {
    // Given
    vi.mocked(useAuthStore).mockReturnValue({
      isAuthenticated: true,
      isLoading: false,
      user: { id: '1', email: '<EMAIL>' },
    } as ReturnType<typeof useAuthStore>)

    // When
    render(<Home />)

    // Then
    expect(mockReplace).toHaveBeenCalledWith('/program')
  })

  it('should delay redirect to /login by 0.8 seconds when not authenticated', async () => {
    // Given
    vi.mocked(useAuthStore).mockReturnValue({
      isAuthenticated: false,
      isLoading: false,
      user: null,
    } as ReturnType<typeof useAuthStore>)

    // When
    render(<Home />)

    // Then - should not redirect immediately
    expect(mockReplace).not.toHaveBeenCalled()

    // Then - should show content during delay
    expect(screen.getByText('Dr. Muscle X')).toBeInTheDocument()
    expect(
      screen.getByText("World's Fastest AI Personal Trainer")
    ).toBeInTheDocument()

    // When - advance timer by 0.8 seconds
    vi.advanceTimersByTime(800)

    // Then - should redirect after delay
    expect(mockReplace).toHaveBeenCalledWith('/login')
  })

  it('should display app name while redirecting', () => {
    // Given
    vi.mocked(useAuthStore).mockReturnValue({
      isAuthenticated: false,
      isLoading: false,
      user: null,
    } as ReturnType<typeof useAuthStore>)

    // When
    render(<Home />)

    // Then
    expect(screen.getByText('Dr. Muscle X')).toBeInTheDocument()
    expect(
      screen.getByText("World's Fastest AI Personal Trainer")
    ).toBeInTheDocument()
  })

  it('should apply theme brand color to the title', () => {
    // Given
    vi.mocked(useAuthStore).mockReturnValue({
      isAuthenticated: false,
      isLoading: false,
      user: null,
    } as ReturnType<typeof useAuthStore>)

    // When
    render(<Home />)

    // Then
    const heading = screen.getByRole('heading', { name: 'Dr. Muscle X' })
    expect(heading).toHaveClass('text-brand-primary')
  })

  it('should apply theme styling to the subtitle', () => {
    // Given
    vi.mocked(useAuthStore).mockReturnValue({
      isAuthenticated: false,
      isLoading: false,
      user: null,
    } as ReturnType<typeof useAuthStore>)

    // When
    render(<Home />)

    // Then
    const subtitle = screen.getByText("World's Fastest AI Personal Trainer")
    expect(subtitle).toHaveClass('text-text-secondary')
  })
})
